

services:
  web:
    build: .
    command: >
      sh -c "python manage.py collectstatic --noinput &&
            gunicorn dcrm.wsgi:application --bind 0.0.0.0:8000 --workers 2"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
    expose:
      - 8000
    env_file:
      - .env.production

  celery:
    build: .
    command: celery -A dcrm worker -l info
    volumes:
      - .:/app
    env_file:
      - .env
    depends_on:
      - web

volumes:
  static_volume:
