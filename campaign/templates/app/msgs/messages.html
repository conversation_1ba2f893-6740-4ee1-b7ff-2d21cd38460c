{% extends "base-app.html" %}

{% block title %}Messages{% endblock %}

{% block content %}
<div class="flex-1 flex flex-col overflow-hidden bg-gray-50 p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
                <p class="text-gray-600">Manage email templates and view performance analytics</p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary" onclick="addNewMessage()">
                    <i class="fa-solid fa-plus mr-2"></i>
                    New Message
                </button>
            </div>
        </div>
    </div>

     <!-- Performance Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Best Performing Subject Line -->
        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Best Subject Line</p>
                    <p class="text-2xl font-bold text-gray-900">42.3%</p>
                    <p class="text-xs text-gray-500 mt-1 truncate">Welcome to CRM Pro - Get Started!</p>
                </div>
                <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-envelope-open text-blue-600"></i>
                </div>
            </div>
        </div>

        <!-- Best Performing CTA -->
        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Best CTA</p>
                    <p class="text-2xl font-bold text-gray-900">38.7%</p>
                    <p class="text-xs text-gray-500 mt-1 truncate">Start Your Free Trial</p>
                </div>
                <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-mouse-pointer text-green-600"></i>
                </div>
            </div>
        </div>

        <!-- Best Performing P.S -->
        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Best P.S</p>
                    <p class="text-2xl font-bold text-gray-900">28.9%</p>
                    <p class="text-xs text-gray-500 mt-1 truncate">P.S. Limited time offer!</p>
                </div>
                <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-star text-purple-600"></i>
                </div>
            </div>
        </div>

        <!-- Overall Performance -->
        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Avg. Open Rate</p>
                    <p class="text-2xl font-bold text-gray-900">24.1%</p>
                    <p class="text-xs text-green-500 mt-1">↑ 12% from last month</p>
                </div>
                <div class="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-chart-line text-orange-600"></i>
                </div>
            </div>
        </div>
    </div>


    <!-- Filters and Search -->
    <div class="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-200">
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex-1 min-w-64">
                <input type="text"
                       placeholder="Search messages by subject, content..."
                       class="input input-bordered w-full bg-white"
                       id="search-messages">
            </div>
            <button onclick="clearMessageFilters()" class="btn btn-ghost">Clear</button>
            <select class="select select-bordered bg-white" id="filter-product">
                <option value="">All Products</option>
                {% for product in products %}
                <option value="{{ product.name|lower }}">{{ product.name }}</option>
                {% endfor %}
            </select>
            <select class="select select-bordered bg-white" id="filter-sort">
                <option value="">Sort By</option>
                <option value="name">Name</option>
                <option value="performance">Performance</option>
                <option value="open-rate">Open Rate</option>
                <option value="click-rate">Click Rate</option>
            </select>
            <div class="text-sm text-gray-500">
                <span id="message-count">{{ messages|length }}</span> messages
            </div>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-left font-semibold text-gray-900">Subject</th>
                        <th class="text-left font-semibold text-gray-900">Product</th>
                        <th class="text-left font-semibold text-gray-900">Performance</th>
                        <th class="text-left font-semibold text-gray-900">Open Rate</th>
                        <th class="text-left font-semibold text-gray-900">Click Rate</th>
                        <th class="text-left font-semibold text-gray-900">Actions</th>
                    </tr>
                </thead>
                <tbody id="messages-table-body">
                    {% for message in messages %}
                    <tr class="hover:bg-gray-50 cursor-pointer message-row" 
                        onclick="editMessage({{ message.id }})"
                        data-product="{{ message.product|lower }}"
                        data-performance="{{ message.performance }}"
                        data-open-rate="{{ message.open_rate }}"
                        data-click-rate="{{ message.click_rate }}"
                        data-name="{{ message.subject|lower }}">
                        <td>
                            <div class="flex items-center gap-3">
                                <div class="w-2 h-2 
                                    {% if message.performance >= 70 %}bg-green-500
                                    {% elif message.performance >= 40 %}bg-yellow-500
                                    {% else %}bg-red-500{% endif %} 
                                    rounded-full" 
                                    title="{% if message.performance >= 70 %}High performing
                                    {% elif message.performance >= 40 %}Medium performing
                                    {% else %}Low performing{% endif %}">
                                </div>
                                <div title="{{message.intro}}">
                                    <div class="font-medium text-gray-900">{{ message.subject }}</div>
                                    <div class="text-sm text-gray-500">{{ message.intro|truncatechars:40 }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-ghost badge-sm">{{ message.product }}</span>
                        </td>
                        <!-- <td>
                            <div class="flex items-center gap-2">
                                <div class="w-16 bg-gray-200 rounded-full h-2">
                                    <div class="
                                        {% if message.performance >= 70 %}bg-green-500
                                        {% elif message.performance >= 40 %}bg-yellow-500
                                        {% else %}bg-red-500{% endif %}
                                        h-2 rounded-full" 
                                        style="width: {{ message.performance }}%">
                                    </div>
                                </div>
                                <span class="text-sm font-medium 
                                    {% if message.performance >= 70 %}text-green-600
                                    {% elif message.performance >= 40 %}text-yellow-600
                                    {% else %}text-red-600{% endif %}">
                                    {{ message.performance }}%
                                </span>
                            </div>
                        </td>
                        <td>
                            <span class="text-sm font-medium text-gray-900">{{ message.open_rate }}%</span>
                        </td>
                        <td>
                            <span class="text-sm font-medium text-gray-900">{{ message.click_rate }}%</span>
                        </td> -->
                         <td>
                            <div class="flex items-center gap-2">
                                <div class="w-16 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">85%</span>
                            </div>
                        </td>
                        <td>
                            <span class="text-sm font-medium text-gray-900">42.3%</span>
                        </td>
                        <td>
                            <span class="text-sm font-medium text-gray-900">18.7%</span>
                        </td>
                        <td>
                            <div class="flex items-center gap-2">
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); duplicateMessage({{ message.id }})" title="Duplicate">
                                    <i class="fa-solid fa-copy"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs" onclick="event.stopPropagation(); previewMessage({{ message.id }})" title="Preview">
                                    <i class="fa-regular fa-eye"></i>
                                </button>
                                <button class="btn btn-ghost btn-xs text-red-500" onclick="event.stopPropagation(); confirmDeleteMessage({{ message.id }})" title="Delete">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr id="no-messages-row">
                        <td colspan="6" class="text-center py-16">
                            <div class="flex flex-col items-center justify-center">
                                <img src="https://raw.githubusercontent.com/Othunderlight/static/refs/heads/main/no-messages.svg"
                                     alt="No messages illustration"
                                     class="w-64 h-auto mx-auto">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2 mt-4">
                                    👋 Create your first message
                                </h3>
                                <p class="text-gray-500 mb-6">
                                    Get started by creating a new email message template
                                </p>
                                <button class="btn btn-primary" onclick="addNewMessage()">
                                    <i class="fa-solid fa-plus mr-2"></i>
                                    New Message
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Message filtering and sorting functionality
function filterMessages() {
    const searchTerm = document.getElementById('search-messages').value.toLowerCase();
    const productFilter = document.getElementById('filter-product').value.toLowerCase();
    const sortFilter = document.getElementById('filter-sort').value;
    
    const rows = document.querySelectorAll('.message-row');
    let visibleCount = 0;

    // First filter the rows
    rows.forEach(row => {
        const messageText = row.textContent.toLowerCase();
        const product = row.getAttribute('data-product').toLowerCase();
        const name = row.getAttribute('data-name').toLowerCase();
        
        const matchesSearch = searchTerm === '' || 
                             messageText.includes(searchTerm) || 
                             name.includes(searchTerm);
        const matchesProduct = productFilter === '' || product === productFilter;

        if (matchesSearch && matchesProduct) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Then sort if a sort option is selected
    if (sortFilter) {
        sortTable(sortFilter);
    }

    // Update empty state
    const noMessagesRow = document.getElementById('no-messages-row');
    if (visibleCount === 0 && rows.length > 0) {
        noMessagesRow.style.display = '';
    } else {
        noMessagesRow.style.display = 'none';
    }

    // Update counter
    document.getElementById('message-count').textContent = `${visibleCount} messages`;
}

function sortTable(sortBy) {
    const tbody = document.getElementById('messages-table-body');
    const rows = Array.from(tbody.querySelectorAll('.message-row[style=""]'));
    
    rows.sort((a, b) => {
        let aValue, bValue;
        
        switch(sortBy) {
            case 'name':
                aValue = a.getAttribute('data-name');
                bValue = b.getAttribute('data-name');
                return aValue.localeCompare(bValue);
            case 'performance':
                aValue = parseFloat(a.getAttribute('data-performance'));
                bValue = parseFloat(b.getAttribute('data-performance'));
                return bValue - aValue; // Descending order
            case 'open-rate':
                aValue = parseFloat(a.getAttribute('data-open-rate'));
                bValue = parseFloat(b.getAttribute('data-open-rate'));
                return bValue - aValue;
            case 'click-rate':
                aValue = parseFloat(a.getAttribute('data-click-rate'));
                bValue = parseFloat(b.getAttribute('data-click-rate'));
                return bValue - aValue;
            default:
                return 0;
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

function clearMessageFilters() {
    document.getElementById('search-messages').value = '';
    document.getElementById('filter-product').value = '';
    document.getElementById('filter-sort').value = '';
    filterMessages();
}

// Action functions
function editMessage(id) {
    window.location.href = `/campaign/messages/edit/${id}`;
}

function addNewMessage() {
    window.location.href = '/campaign/messages/add/?add_new=true';
}

function duplicateMessage(id) {
    ModalSystem.toast('Message duplicated successfully!', 'success');
    // In a real app, this would make an API call to duplicate the message
}

function previewMessage(id) {
    ModalSystem.toast('Preview functionality coming soon!', 'info');
    // In a real app, this would open a modal with message preview
}

function confirmDeleteMessage(id) {
    ModalSystem.confirm({
        title: 'Delete Message',
        message: 'Are you sure you want to delete this message template? This action cannot be undone.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            fetch(`/api/messages/${id}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    ModalSystem.toast('Error deleting message', 'error');
                    throw new Error('Failed to delete message');
                }
                const row = document.querySelector(`tr[onclick="editMessage(${id})"]`);
                if (row) row.remove();

                counter= document.getElementById("message-count")
                counter.innerHTML= counter.innerHTML - 1;
                
                
                ModalSystem.toast('Message deleted successfully!', 'success');
                return response;
            })
            .catch(error => {
                console.error('Error deleting message:', error);
                throw error;
            });
        }
    });
}


// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for filters
    document.getElementById('search-messages').addEventListener('input', filterMessages);
    document.getElementById('filter-product').addEventListener('change', filterMessages);
    document.getElementById('filter-sort').addEventListener('change', function() {
        filterMessages(); // This will trigger sorting too
    });
    
    // Hide empty state if we have messages
    const noMessagesRow = document.getElementById('no-messages-row');
    if (noMessagesRow && document.querySelectorAll('.message-row').length > 0) {
        noMessagesRow.style.display = 'none';
    }
});
</script>
{% endblock %}