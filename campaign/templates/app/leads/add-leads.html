{% extends "base.html" %}
{% block title %}Add Leads{% endblock %}

{% block base-content %}
<div class="flex h-full font-sans">
  <div class="flex w-full flex-col">
    <main class="flex-1 main-content-bg pl-[100px] pr-[100px] transition-all duration-500 ease-in-out overflow-y-auto">
        <div class="container mx-auto px-4 py-12">
        <!-- Back Button -->
            <div id="back-btn-container" class="mb-6">
                <a href="/campaign/leads/lists/" class="btn btn-ghost btn-md gap-2 text-base-content/70 hover:text-base-content transition-all duration-200"> 
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Back
                </a>
            </div>

            <!-- Centered Content -->
            <div class="flex flex-col items-center justify-center min-h-[60vh]" id="mainContent">
            <div class="w-full max-w-xl">
                <!-- Initial Choice View -->
                <div id="choiceView" class="space-y-10">
                <!-- Header -->
                <div class="text-center space-y-3 transition-opacity duration-300">
                    <h1 class="text-4xl font-bold text-base-content">Let's add new leads</h1>
                    <p id="descText" class="text-base-content/60 text-lg">Wanna add leads individually or in bulk?</p>
                </div>

                <!-- Options -->
                <div class="space-y-3">
                    <button class="w-full p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 flex items-center gap-3"
                            onclick="showIndividualForm()">
                    <div class="w-10 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
                        <i class="fa-solid fa-user-plus text-red-600 text-lg"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-medium text-gray-900">Individually</div>
                        <div class="text-sm text-gray-500">using a form</div>
                    </div>
                    </button>
                    <button class="w-full p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 flex items-center gap-3"
                            onclick="showBulkForm()">
                    <div class="w-10 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
                        <i class="fa-solid fa-file-csv text-success text-lg"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-medium text-gray-900">Bulk importing</div>
                        <div class="text-sm text-gray-500">from a csv file</div>
                    </div>
                    </button>
                </div>
                </div>

                <!-- Individual Form View (hidden initially) -->
                <div id="individualFormView" class="hidden">
                <div class="text-center space-y-3 mb-6">
                    <h1 class="text-4xl font-bold text-base-content">Add New Lead</h1>
                    <p class="text-base-content/60 text-lg">Fill in the lead details step by step</p>
                    
                    <!-- Progress Steps -->
                    <div class="steps steps-horizontal w-full my-6">
                    <div id="step1" class="step step-primary">Personal</div>
                    <div id="step2" class="step">Company</div>
                    <div id="step3" class="step">Source</div>
                    </div>
                </div>

                <div class="max-h-[60vh] overflow-y-auto pr-4">
                    <div id="AddLeadsForm" class="space-y-6 pb-4" action="event.preventDefault();" onsubmit="event.preventDefault();">
                    <!-- Section 1: Personal Information -->
                    <div id="section1" class="form-section">
                        <div class="space-y-4">
                        <div class="form-control">
                            <label class="label" for="full_name">
                            <span class="label-text">Full Name <span class="text-error">*</span></span>
                            </label>
                            <input type="text" id="full_name" name="full_name" class="input input-bordered w-full" required 
                            onchange="populateNames()" />
                            <label class="label">
                            <span class="label-text-alt text-info">Will be used to auto-fill first/last name if empty</span>
                            </label>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                            <label class="label" for="first_name">
                                <span class="label-text">First Name</span>
                            </label>
                            <input type="text" id="first_name" name="first_name" class="input input-bordered w-full" />
                            </div>
                            
                            <div class="form-control">
                            <label class="label" for="last_name">
                                <span class="label-text">Last Name</span>
                            </label>
                            <input type="text" id="last_name" name="last_name" class="input input-bordered w-full" />
                            </div>
                        </div>
                        
                        <div class="form-control">
                            <label class="label" for="position">
                            <span class="label-text">Position <span class="text-error">*</span></span>
                            </label>
                            <input type="text" id="position" name="position" class="input input-bordered w-full" required />
                        </div>
                        
                        <div class="form-control">
                            <label class="label" for="email">
                            <span class="label-text">Email <span class="text-error">*</span></span>
                            </label>
                            <input type="email" id="email" name="email" class="input input-bordered w-full" required />
                        </div>
                        
                        <div class="form-control">
                            <label class="label" for="phone">
                            <span class="label-text">Phone Number</span>
                            </label>
                            <input type="tel" id="phone" name="phone" class="input input-bordered w-full" />
                        </div>
                        
                        <div class="form-control">
                            <label class="label" for="linkedin">
                            <span class="label-text">LinkedIn Profile</span>
                            </label>
                            <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                <i class="fab fa-linkedin"></i>
                            </span>
                            <input type="url" id="linkedin" name="linkedin" class="input input-bordered w-full pl-10" />
                            </div>
                        </div>
                        </div>
                    </div>
                    
                    <!-- Section 2: Company Information -->
                    <div id="section2" class="form-section hidden">
                        <div class="space-y-4">
                          <div class="form-control">
                              <label class="label" for="company_name">
                              <span class="label-text">Company Name <span class="text-error">*</span></span>
                              </label>
                              <input type="text" id="company_name" name="company_name" class="input input-bordered w-full" required />
                          </div>
                          
                          <div class="form-control">
                              <label class="label" for="company_website">
                              <span class="label-text">Company Website</span>
                              </label>
                              <div class="relative">
                              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                  <i class="fas fa-globe"></i>
                              </span>
                              <input type="url" id="company_website" name="company_website" class="input input-bordered w-full pl-10" />
                              </div>
                          </div>
                          
                          <div class="form-control">
                              <label class="label" for="industry">
                              <span class="label-text">Industry</span>
                              </label>
                              <input type="text" id="industry" name="industry" class="input input-bordered w-full" />
                          </div>
                          
                          <div class="form-control">
                              <label class="label" for="employee_count">
                              <span class="label-text">Employee Count</span>
                              </label>
                              <select id="employee_count" name="employee_count" class="select select-bordered w-full">
                              <option value="" selected disabled>Select employee count</option>
                              <option value="1-10">1-10</option>
                              <option value="11-50">11-50</option>
                              <option value="51-200">51-200</option>
                              <option value="201-500">201-500</option>
                              <option value="501-1000">501-1000</option>
                              <option value="1000+">1000+</option>
                              </select>
                          </div>

                          <div class="form-control">
                            <label class="label" for="campany_linkedin_page">
                            <span class="label-text">campany linkedin page</span>
                            </label>
                            <input type="url" id="campany_linkedin_page" name="campany_linkedin_page" class="input input-bordered w-full" />
                          </div>

                          <div class="form-control">
                            <label class="label" for="location">
                            <span class="label-text">Location</span>
                            </label>
                            <input type="text" id="location" name="location" class="input input-bordered w-full" />
                          </div>

                        </div>
                    </div>
                    
                    <!-- Section 3: Lead Source Details -->
                    <div id="section3" class="form-section hidden">
                        <div class="space-y-4">
                        <div class="form-control">
                            <label class="label" for="source">
                            <span class="label-text">Source <span class="text-error">*</span></span>
                            </label>
                            <select id="source" name="source" class="select select-bordered w-full" required>
                            <option value="" selected disabled>Select lead source</option>
                            <option value="linkedin_scrape">LinkedIn Scrape</option>
                            <option value="social">Social Media</option>
                            <option value="newsletter">Newsletter Opt-in</option>
                            <option value="form">Free Consultation Form</option>
                            </select>
                        </div>
                        
                        <div class="form-control">
                            <label class="label" for="lead_type">
                            <span class="label-text">Lead Type <span class="text-error">*</span></span>
                            </label>
                            <select id="lead_type" name="lead_type" class="select select-bordered w-full" required>
                            <option value="" selected disabled>Select lead type</option>
                            <option value="cold">Cold</option>
                            <option value="warm">Warm</option>
                            <option value="hot">Hot</option>
                            <option value="customer">Customer</option>
                            </select>
                        </div>
                        
                        <div class="form-control">
                            <label class="label" for="tags">
                            <span class="label-text">Additional Tags</span>
                            </label>
                            <input type="text" id="tags" name="tages" class="input input-bordered w-full" />
                            <label class="label">
                            <span class="label-text-alt text-info">Seperate multiple tags with spaces</span>
                            </label>
                        </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div id="actionButtons" class="flex gap-4 justify-between pt-4 sticky bottom-0 bg-base-100 pb-4">
                        <button type="button" class="btn btn-ghost btn-md hidden" id="backBtn">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        Back
                        </button>
                        
                        <button type="button" class="btn btn-primary btn-md ml-auto gap-2 text-gray-100" id="nextBtn">
                        Next
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        </button>
                        
                        <button class="btn btn-success btn-md ml-auto gap-2 text-gray-100 hidden" id="submitIndividualBtn" onclick="event.preventDefault(); submitIndividualForm({{request.user.subscribed_company.id}}, {{lead_list_id}})">
                        <i class="fas fa-check"></i>
                          Submit Lead
                        </button>
                    </div>
                  </div>
                </div>
                </div>

            </div>

            </div>

            <div id="bulkFormView" class="hidden">   

                      <div class="flex-1 flex flex-col bg-gray-50 p-8">

                        <div class="flex justify-between items-center">
                          <div class="mb-8">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Upload Leads CSV</h1>
                            <p class="text-gray-600">Upload your leads in bulk using a CSV file</p>
                          </div>
                          <div class="flex gap-2">
                              <!-- Download Template Button -->
                            <div class="text-center">
                                <button id="downloadTemplate" class="btn bg-primary/90 text-gray-100 border border-base-200 rounded-lg px-4 text-l hover:bg-success transition-all duration-300"> 
                                    Download Sample File to fill in
                                </button>
                            </div>
                          </div>
                      </div>

                        <!-- Main Content -->
                        <div class="flex flex-col gap-8">
                            <!-- Instructions Section -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                <h2 class="text-xl font-semibold mb-4">Instructions</h2>
                                <div class="space-y-4">
                                    <div>
                                        <h3 class="font-bold text-gray-800 mb-2">Accepted Fields:</h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                            <div class="flex items-start gap-2">
                                                <span class="text-success">•</span>
                                                <div>
                                                    <strong>Full name:</strong> <span class="text-gray-500">(Required)</span>
                                                </div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>First name</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>Last name</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>Position</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-success">•</span>
                                                <div>
                                                    <strong>Email:</strong> <span class="text-gray-500">(Required)</span>
                                                </div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>Phone number</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>Linkedin profile</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-success">•</span>
                                                <div>
                                                    <strong>Company name:</strong> <span class="text-gray-500">(Required)</span>
                                                </div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>Company website</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>Industry</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>employee_count</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>campany_linkedin_page</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-gray-400">•</span>
                                                <div>location</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-warning">•</span>
                                                <div>
                                                    <strong>source:</strong> <span class="text-gray-500">(Choices: linkedin_scrape, social, newsletter, form, other)</span>
                                                </div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="text-warning">•</span>
                                                <div>
                                                    <strong>lead_type:</strong> <span class="text-gray-500">(Choices: cold, warm, hot, customer, other)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="divider"></div>

                                    <div>
                                        <h3 class="font-bold text-gray-800 mb-2">Important Notes:</h3>
                                        <div class="space-y-3">
                                            <div class="flex items-start gap-2">
                                                <span class="font-bold text-gray-700">1.</span>
                                                <div>You must download the file and plug your leads info in it</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="font-bold text-gray-700">2.</span>
                                                <div>The required fields are must, and for non required fields leave them empty and don't delete them from the fields</div>
                                            </div>
                                            <div class="flex items-start gap-2">
                                                <span class="font-bold text-gray-700">3.</span>
                                                <div>Don't change the order of the columns in the file</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload Section -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                <h2 class="text-xl font-semibold mb-6">Upload CSV File</h2>
                                
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50 transition-colors" id="uploadArea">
                                    <input type="file" id="csvFile" accept=".csv" class="file-input file-input-primary" >
                                </div>
                                
                                <div class="file-processed flex items-center justify-center mt-4 text-success hidden" id="processedIndicator">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    File processed and ready for upload
                                </div>
                            </div>

                            <!-- Validation Results -->
                            <div id="validationResults" class="hidden">
                                <div class="alert hidden" id="validationAlert">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span id="validationMessage"></span>
                                </div>

                                <div class="overflow-x-auto mt-4 hidden" id="previewTableContainer">
                                    <table class="table">
                                    <thead id="previewTableHead"></thead>
                                    <tbody id="previewTableBody"></tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Upload Button -->
                            <div class="text-center hidden" id="bulkActionsContainer">
                                <div class="mb-4">
                                    <span class="text-gray-600">Detected </span>
                                    <span id="rowCount" class="font-bold text-success">0</span>
                                    <span class="text-gray-600"> leads</span>
                                </div>
                                <button class="upload-btn flex items-center justify-center mx-auto btn btn-success text-gray-100 hidden" id="submitBulkBtn" disabled>
                                    UPLOAD ALL
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

            </div>
        </div>  
    </main>
  </div>
</div>

{% include 'components/modals.html' %}

<script>
  window.CompanyID = {{ request.user.subscribed_company.id }};
  window.ListID = {{ lead_list_id }};
  let currentSection = 1;
  const totalSections = 3;
  
  // Show initial choice view
  function showChoiceView() {
    document.getElementById('choiceView').classList.remove('hidden');
    document.getElementById('individualFormView').classList.add('hidden');
  }
  
  // Show individual form
  function showIndividualForm() {
    document.getElementById('choiceView').classList.add('hidden');
    document.getElementById('individualFormView').classList.remove('hidden');
    currentSection = 1;
    showSection(currentSection);
    updateNavigation();
    document.getElementById('back-btn-container').classList.remove('mb-20');
    document.getElementById('back-btn-container').classList.add('mb-6');
  }
  
  // Show bulk form (placeholder for future implementation)
  function showBulkForm() {
    document.getElementById('mainContent').style.display = 'none';
    document.getElementById('choiceView').classList.remove('hidden');
    document.getElementById('bulkFormView').classList.remove('hidden');
    document.getElementById('back-btn-container').classList.remove('mb-20');
    document.getElementById('back-btn-container').classList.add('mb-2');
  }
  
  
  // Initialize form
  document.addEventListener('DOMContentLoaded', function() {
    updateNavigation();
  });
  
  // Next button click handler
  document.getElementById('nextBtn').addEventListener('click', function() {
    if (validateSection(currentSection)) {
      currentSection++;
      showSection(currentSection);
      updateNavigation();
    }
  });
  
  // Back button click handler
  document.getElementById('backBtn').addEventListener('click', function() {
    currentSection--;
    showSection(currentSection);
    updateNavigation();
  });
  
  // Show the current section and hide others
  function showSection(sectionNumber) {
    document.querySelectorAll('.form-section').forEach(section => {
      section.classList.add('hidden');
    });
    document.getElementById(`section${sectionNumber}`).classList.remove('hidden');
    
    // Update progress steps
    document.querySelectorAll('.step').forEach((step, index) => {
      if (index + 1 <= sectionNumber) {
        step.classList.add('step-primary');
      } else {
        step.classList.remove('step-primary');
      }
    });
    
    // Scroll to top of form
    document.querySelector('#individualFormView .overflow-y-auto').scrollTo(0, 0);
  }
  
  // Update navigation buttons based on current section
  function updateNavigation() {
    const backBtn = document.getElementById('backBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitIndividualBtn = document.getElementById('submitIndividualBtn');
    
    backBtn.classList.toggle('hidden', currentSection === 1);
    nextBtn.classList.toggle('hidden', currentSection === totalSections);
    submitIndividualBtn.classList.toggle('hidden', currentSection !== totalSections);
  }
  
  // Validate current section before proceeding
  function validateSection(sectionNumber) {
    const section = document.getElementById(`section${sectionNumber}`);
    const requiredInputs = section.querySelectorAll('[required]');
    let isValid = true;
    
    requiredInputs.forEach(input => {
      if (!input.value.trim()) {
        input.classList.add('input-error');
        isValid = false;
      } else {
        input.classList.remove('input-error');
      }
    });
    
    if (!isValid) {
      alert('Please fill in all required fields before proceeding.');
    }
    
    return isValid;
  }
  
  // Auto-populate first and last names from full name
  function populateNames() {
    const fullName = document.getElementById('full_name').value.trim();
    if (!fullName) return;
    
    const nameParts = fullName.split(' ');
    const firstName = document.getElementById('first_name');
    const lastName = document.getElementById('last_name');
    
    if (!firstName.value) {
      firstName.value = nameParts[0];
    }
    
    if (!lastName.value && nameParts.length > 1) {
      lastName.value = nameParts.slice(1).join(' ');
    }
  }
  
  // Form submission handler
  function submitIndividualForm(CompanyID, ListID) {
      if (validateSection(currentSection)) {
          // Show loading state (using toast instead of modal)
          
          // Collect form data
          const leadData = {
              full_name: document.getElementById('full_name').value,
              first_name: document.getElementById('first_name').value,
              last_name: document.getElementById('last_name').value,
              position: document.getElementById('position').value,
              email: document.getElementById('email').value,
              phone_number: document.getElementById('phone').value,
              linkedin_profile: document.getElementById('linkedin').value,
              company_name: document.getElementById('company_name').value,
              company_website: document.getElementById('company_website').value,
              industry: document.getElementById('industry').value,
              employee_count: document.getElementById('employee_count').value,
              campany_linkedin_page: document.getElementById('campany_linkedin_page').value,
              location: document.getElementById('location').value,
              source: document.getElementById('source').value,
              lead_type: document.getElementById('lead_type').value,
              tags: document.getElementById('tags').value,
              lead_list: ListID,
              subscribed_company: CompanyID
          };

          // Make API call
          fetch('/api/leads/', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': getCookie('csrftoken')
              },
              body: JSON.stringify(leadData)
          })
          .then(response => {
              if (!response.ok) {
                  return response.json().then(err => { throw err; });
              }
              return response.json();
          })
          .then(data => {
              // Hide loading state
              ModalSystem.toast('Lead submitted successfully!', 'success'); 
               // Wait 3 seconds before redirecting
              setTimeout(() => {
                  window.location.href = `/campaign/leads/${ListID}/`;
              }, 3000);
          })
          .catch(error => {
              // ModalSystem.hideLoading();
              let errorMessage = 'Failed to submit lead';
              
              if (error.detail) {
                  errorMessage = error.detail;
              } else if (error.email) {
                  errorMessage = error.email[0]; // Assuming email validation error
              }
              
              ModalSystem.toast(errorMessage, 'error');
              console.error('Error:', error);
          });

          return true;
      }
      return false;
  }


document.addEventListener('DOMContentLoaded', function() {
  const requiredFields = ['full_name', 'email', 'company_name', 'source', 'lead_type'];
  const sourceChoices = ['linkedin_scrape', 'social', 'newsletter', 'form', 'other'];
  const leadTypeChoices = ['cold', 'warm', 'hot', 'customer', 'other'];
  const expectedColumns = [
    'full_name', 'first_name', 'last_name', 'position', 'email', 
    'phone_number', 'linkedin_profile', 'company_name', 'company_website', 
    'industry', 'employee_count', 'campany_linkedin_page', 'location', 
    'source', 'lead_type', 'tags'
  ];

  // Download template button
  document.getElementById('downloadTemplate').addEventListener('click', function() {
    const csvHeader = expectedColumns.join(',');
    const csvContent = "data:text/csv;charset=utf-8," + csvHeader + "\n,,,,,,,,,,,,,,";
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "leads_template.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  // File upload handler
  document.getElementById('csvFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
      const contents = e.target.result;
      parseCSV(contents);
    };
    reader.readAsText(file);
  });

  function parseCSV(csvText) {
    const lines = csvText.split('\n').filter(line => line.trim() !== '');
    if (lines.length < 1) {
      showValidationError("The CSV file is empty.");
      return;
    }

    const headers = lines[0].split(',').map(h => h.trim());
    
    // Validate headers
    if (headers.length !== expectedColumns.length) {
      showValidationError(`The CSV file has ${headers.length} columns but expected ${expectedColumns.length}.`);
      return;
    }

    for (let i = 0; i < expectedColumns.length; i++) {
      if (headers[i] !== expectedColumns[i]) {
        showValidationError(`Column ${i+1} should be "${expectedColumns[i]}" but found "${headers[i]}". Please don't change the column order or names.`);
        return;
      }
    }

    // Parse data rows
    const data = [];
    let errors = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]);
      const row = {};
      let rowErrors = [];

      // Map values to headers
      for (let j = 0; j < headers.length; j++) {
        row[headers[j]] = values[j] ? values[j].trim() : '';
      }

      // Validate required fields
      for (const field of requiredFields) {
        if (!row[field]) {
          rowErrors.push(`Row ${i}: "${field}" is required.`);
        }
      }

      // Validate email format if present
      if (row.email && !isValidEmail(row.email)) {
        rowErrors.push(`Row ${i}: "email" is not valid.`);
      }

      // Validate source choices
      if (row.source && !sourceChoices.includes(row.source)) {
        rowErrors.push(`Row ${i}: "source" must be one of: ${sourceChoices.join(', ')}`);
      }

      // Validate lead_type choices
      if (row.lead_type && !leadTypeChoices.includes(row.lead_type)) {
        rowErrors.push(`Row ${i}: "lead_type" must be one of: ${leadTypeChoices.join(', ')}`);
      }

      // Validate URLs if present
      if (row.linkedin_profile && !isValidURL(row.linkedin_profile)) {
        rowErrors.push(`Row ${i}: "linkedin_profile" is not a valid URL.`);
      }
      if (row.company_website && !isValidURL(row.company_website)) {
        rowErrors.push(`Row ${i}: "company_website" is not a valid URL.`);
      }
      if (row.campany_linkedin_page && !isValidURL(row.campany_linkedin_page)) {
        rowErrors.push(`Row ${i}: "campany_linkedin_page" is not a valid URL.`);
      }

      if (rowErrors.length === 0) {
        data.push(row);
      } else {
        errors = errors.concat(rowErrors);
      }
    }

    if (errors.length > 0) {
      showValidationError(errors.join('<br>'));
    } else {
      const numberOfRows = lines.length - 1;
      document.getElementById('rowCount').textContent = numberOfRows
      document.getElementById('bulkActionsContainer').classList.remove("hidden")
      showValidationSuccess(data);
    }
  }

  function parseCSVLine(line) {
    const values = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current);
        current = '';
      } else {
        current += char;
      }
    }

    values.push(current);
    return values;
  }

  function isValidEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }

  function isValidURL(url) {
    try {
      // Add https:// in front of www
      if (url.startsWith('www.')) {
        url = 'https://' + url;
      }
      new URL(url);
      return true;
    } catch (_) {
      return false;
    }
  }

  function showValidationError(message) {
    const validationResults = document.getElementById('validationResults');
    const validationAlert = document.getElementById('validationAlert');
    const validationMessage = document.getElementById('validationMessage');
    const successAlert = document.getElementById('successAlert');
    const previewTableContainer = document.getElementById('previewTableContainer');
    const submitBulkBtn = document.getElementById('submitBulkBtn');

    validationResults.classList.remove('hidden');
    validationAlert.classList.remove('hidden');
    validationAlert.classList.add('alert-error');
    validationMessage.innerHTML = message;
    successAlert.classList.add('hidden');
    previewTableContainer.classList.add('hidden');
    submitBulkBtn.classList.add('hidden');
    submitBulkBtn.disabled = true;
  }

  function normalizeURL(url) {
    if (!url) return '';
    if (!/^https?:\/\//i.test(url)) {
      return 'https://' + url;
    }
    return url;
  }


  function showValidationSuccess(data) {
    const validationResults = document.getElementById('validationResults');
    const validationAlert = document.getElementById('validationAlert');
    const successAlert = document.getElementById('successAlert');
    const previewTableContainer = document.getElementById('previewTableContainer');
    const previewTableHead = document.getElementById('previewTableHead');
    const previewTableBody = document.getElementById('previewTableBody');
    const submitBulkBtn = document.getElementById('submitBulkBtn');

    validationResults.classList.remove('hidden');
    validationAlert.classList.add('hidden');
    ModalSystem.toast(`CSV file is valid! You can now submit the form.`);
    previewTableContainer.classList.remove('hidden');
    submitBulkBtn.classList.remove('hidden');
    submitBulkBtn.disabled = false;

    // Clear previous table content
    previewTableHead.innerHTML = '';
    previewTableBody.innerHTML = '';

    // Create table header
    const headRow = document.createElement('tr');
    for (const key in data[0]) {
      const th = document.createElement('th');
      th.textContent = key;
      headRow.appendChild(th);
    }
    previewTableHead.appendChild(headRow);

    // Create table body (show first 5 rows)
    const displayRows = data.slice(0, 5);
    displayRows.forEach(row => {
      const bodyRow = document.createElement('tr');
      for (const key in row) {
        const td = document.createElement('td');
        td.textContent = row[key] || '-';
        bodyRow.appendChild(td);
      }
      previewTableBody.appendChild(bodyRow);
    });

    if (data.length > 5) {
      const moreRow = document.createElement('tr');
      const moreCell = document.createElement('td');
      moreCell.colSpan = Object.keys(data[0]).length;
      moreCell.className = 'text-center italic';
      moreCell.textContent = `... and ${data.length - 5} more rows`;
      moreRow.appendChild(moreCell);
      previewTableBody.appendChild(moreRow);
    }

    // Set up submit button
    submitBulkBtn.onclick = function() {
      const CompanyID = window.CompanyID;
      const ListID = window.ListID;
      // Append these fields to each row
      const enrichedData = data.map(row => ({
        ...row,
        lead_list: ListID,
        subscribed_company: CompanyID,
        linkedin_profile: normalizeURL(row.linkedin_profile),
        company_website: normalizeURL(row.company_website),
        campany_linkedin_page: normalizeURL(row.campany_linkedin_page)

      }));

      ModalSystem.toast('Submitting leads...', 'info');

      fetch('/api/leads/bulk-create/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(enrichedData)
      })
      .then(response => {
        if (!response.ok) {
          return response.json().then(err => { throw err; });
        }
        return response.json();
      })
      .then(result => {
        ModalSystem.toast(`✅ ${data.length} leads uploaded successfully!`, 'success');
        setTimeout(() => {
          window.location.href = `/campaign/leads/${ListID}/`;
        }, 3000);
      })
      .catch(error => {
        console.error('Upload failed:', error);
        let message = '❌ Failed to upload leads.';
        if (error && typeof error === 'object') {
          message += ' Check your CSV format.';
        }
        ModalSystem.toast(message, 'error');
      });
    };

  }
});


function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>

<style>
/* Custom scrollbar for the card if needed */
.card {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.card::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.card::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.card::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Table styling */
.table-zebra tbody tr:nth-child(odd) {
  background-color: #f5f5f5;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}
</style>
{% endblock %}