{% extends "app/campaign/campaign-base.html" %}

{% block title %}Campaign Sequence{% endblock %}

{% block cmp-base-content %}
<style>
    /* Message Grid Styles */
    .message-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
        padding: 1rem;
    }
    
    .message-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1.25rem;
        transition: all 0.2s;
        cursor: pointer;
        background-color: white;
    }
    
    .message-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .stats-bar {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
        font-size: 0.875rem;
    }

    /* Table Styles */
    .table-zebra tbody tr:nth-child(even) {
        background-color: #f9fafb;
    }

    /* Stats Cards */
    .stats {
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
    }
</style>

<div class="flex flex-col h-full">
    <!-- Header -->
    <div class="flex items-center justify-between mt-8">
        <div class="flex items-center gap-4">
            <h3 class="text-xl font-semibold">Message Sequences</h3>
        </div>
        <div class="flex items-center gap-3">  
            <button class="btn btn-primary h-10 min-h-10 rounded-lg px-4 text-sm" onclick="document.getElementById('add-modal').showModal()">
                Assign Message
            </button>
        </div>
    </div>

    <!-- Message Grid -->
    <div class="message-grid">
        {% for message in messages %}
        <div class="message-card" style="padding-bottom: 10px;  padding-right: 15px; min-width: 350px;" onclick="openMessageModal({{ message.id }})">
            <div class="flex justify-between items-start">
                <h3 class="font-medium text-gray-900">{{ message.subject }}</h3>
                <div class="text-right">
                    <span class="text-2xl font-bold pr-3 pl-3">{{ message.total_assignments }}</span>
                </div>
            </div>

            <p class="text-sm text-gray-500 mt-5">
                {% if message.delayed_by_days == 0 %}
                    this is the first message in this sequence.
                {% else %}
                    this message will be send in {{ message.delayed_by_days }} days, after the last one.
                {% endif %}        
            </p>

            <div class="divider mt-2 mb-2"></div>
            
            <div class="card-actions justify-between">
                <div class="stats-bar mb-4 flex-col">
                    <span class="text-blue-600">
                        {{ message.sent_count }} sent
                    </span>
                    <span class="text-green-600">
                        {{ message.response_count }} responses
                    </span>
                </div>
                <div class="stats-bar flex-col">
                    <button class="btn btn-sm btn-ghost text-error" onclick="event.preventDefault(); event.stopPropagation(); ConfirmBulkDelete({{message.id}}, {{campaign_id}})" title="Delete">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full text-center py-12">
            <div class="text-gray-400 mb-4">
                <i class="fas fa-envelope-open-text fa-3x"></i>
            </div>
            <h3 class="text-lg font-medium">No messages assigned yet</h3>
            <button class="btn btn-primary mt-4" onclick="document.getElementById('add-modal').showModal()">
                Assign Message
            </button>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Assign Message Modal -->
<dialog id="add-modal" class="modal">
    <div class="modal-box max-w-md">
        <div class="flex flex-col gap-4">
            <!-- Header -->
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900">Assign Message to Sequence</h3>
            </div>
            
            <!-- Message Selection -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">Select Message</span>
                </label>
                <select class="select select-bordered w-full focus:ring-2 focus:ring-primary" id="message-select">
                    {% for msg in all_messages %}
                        <option value="{{ msg.id }}">{{ msg.subject }}</option>
                    {% empty %}
                        <option>no message found</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="divider my-0"></div>
            
            <!-- Delay Input -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">Days After Previous Message</span>
                </label>
                <div class="flex items-center gap-2">
                    <input name="number" id="delayed-from-last-message" 
                           type="number" 
                           class="input input-bordered w-24 text-center focus:ring-2 focus:ring-primary" 
                           min="0" 
                           value="0" 
                           required>
                    <span class="text-sm text-gray-500">days</span>
                </div>
                <label class="label">
                    <span class="label-text-alt text-gray-500">Number of days after the previous message in sequence</span>
                </label>
            </div>
            
            <!-- Actions -->
            <div class="modal-action mt-2">
                <button class="btn btn-primary" onclick="AssignMessage2Sequence({{campaign_id}})">Assign</button>
                <form method="dialog">
                    <button class="btn btn-ghost">Cancel</button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Backdrop -->
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<!-- Assignments Modal -->
<dialog id="assignment-modal" class="modal">
    <div class="modal-box w-11/12 max-w-5xl">
        <div class="flex justify-between items-center mb-4">
            <h3 class="font-bold text-lg">Message Assignments</h3>
            <form method="dialog">
                <button class="btn btn-sm btn-circle">✕</button>
            </form>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="stats bg-primary text-primary-content">
                <div class="stat">
                    <div class="stat-title">Total Assignments</div>
                    <div class="stat-value" id="total-assignments">0</div>
                </div>
            </div>
            <div class="stats bg-success text-success-content">
                <div class="stat">
                    <div class="stat-title">Sent</div>
                    <div class="stat-value" id="sent-count">0</div>
                </div>
            </div>
            <div class="stats bg-info text-info-content">
                <div class="stat">
                    <div class="stat-title">Responses</div>
                    <div class="stat-value" id="response-count">0</div>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Lead</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Sent At</th>
                        <th>Response</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="assignments-table-body">
                    <!-- Will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="modal-action">
            <button class="btn btn-primary" onclick="exportAssignments()">
                <i class="fas fa-download mr-2"></i> Export
            </button>
            <form method="dialog">
                <button class="btn btn-ghost">Close</button>
            </form>
        </div>
    </div>
    <form method="dialog" class="modal-backdrop">
        <button>close</button>
    </form>
</dialog>

<script>
    // Assignments Modal
    let currentMessageId = null;

    function openMessageModal(messageId) {
        currentMessageId = messageId;
        const campaign_id = {{ campaign_id }};
        window.ModalSystem.loading('Loading assignments...');

        const url = "{% url 'campaign_sequence_message' 999 %}".replace('999', messageId);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}',
            },
            body: JSON.stringify({
                campaign_id: campaign_id
            })
        })
        .then(response => {
            if (!response.ok) throw new Error('Network response was not ok');
            return response.json();
        })
        .then(data => {
            renderAssignmentModal(data);
            document.getElementById('assignment-modal').showModal();
            window.ModalSystem.hideLoading();
        })
        .catch(error => {
            console.error('Error:', error);
            window.ModalSystem.hideLoading();
            window.ModalSystem.error('Failed to load assignments: ' + error.message);
        });
    }

    function renderAssignmentModal(assignments) {
        // Update stats
        document.getElementById('total-assignments').textContent = assignments.length;
        document.getElementById('sent-count').textContent = 
            assignments.filter(a => a.sent).length;
        document.getElementById('response-count').textContent = 
            assignments.filter(a => a.responded).length;

        // Render table
        const tableBody = document.getElementById('assignments-table-body');
        tableBody.innerHTML = '';

        if (assignments.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-gray-500">
                        No assignments found for this message
                    </td>
                </tr>
            `;
            return;
        }

        assignments.forEach(assignment => {
            const row = document.createElement('tr');
            
            // Lead Name
            const nameCell = document.createElement('td');
            nameCell.textContent = `${assignment.campaign_lead__lead__first_name} ${assignment.campaign_lead__lead__last_name}`;
            row.appendChild(nameCell);
            
            // Email
            const emailCell = document.createElement('td');
            emailCell.textContent = assignment.campaign_lead__lead__email;
            row.appendChild(emailCell);
            
            // Status
            const statusCell = document.createElement('td');
            const statusBadge = document.createElement('span');
            statusBadge.className = `badge ${assignment.sent ? 'badge-success' : 'badge-warning'}`;
            statusBadge.textContent = assignment.sent ? 'Sent' : 'Pending';
            statusCell.appendChild(statusBadge);
            row.appendChild(statusCell);
            
            // Sent At
            const sentAtCell = document.createElement('td');
            sentAtCell.textContent = assignment.sent_at ? 
                new Date(assignment.sent_at).toLocaleString() : 'Not sent';
            row.appendChild(sentAtCell);
            
            // Response
            const responseCell = document.createElement('td');
            if (assignment.responded) {
                responseCell.innerHTML = '<span class="badge badge-info">Responded</span>';
            } else {
                responseCell.innerHTML = '<span class="text-gray-400">-</span>';
            }
            row.appendChild(responseCell);
            
            // Actions
            const actionsCell = document.createElement('td');
            actionsCell.className = 'flex gap-2';
            
            const viewBtn = document.createElement('button');
            viewBtn.className = 'btn btn-xs btn-ghost';
            viewBtn.innerHTML = '<i class="fas fa-eye"></i>';
            viewBtn.onclick = (e) => {
                e.stopPropagation();
                viewAssignmentDetails(assignment.id);
            };
            actionsCell.appendChild(viewBtn);
            
            const resendBtn = document.createElement('button');
            resendBtn.className = 'btn btn-xs btn-ghost';
            resendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            resendBtn.onclick = (e) => {
                e.stopPropagation();
                resendAssignment(assignment.id);
            };
            actionsCell.appendChild(resendBtn);
            
            row.appendChild(actionsCell);
            tableBody.appendChild(row);
        });
    }


    function ConfirmBulkDelete(messageId, campaignId) {
        ModalSystem.confirm({
            title: 'Delete Assigned Message',
            message: 'Are you sure you want to delete this Assigned Message? This action cannot be undone.',
            confirmText: 'Delete',
            confirmClass: 'btn-error',
            action: function() {
                fetch("/api/message-assignments/bulk-delete-by-message/", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": '{{ csrf_token }}',
                    },
                    body: JSON.stringify({
                        message_id: messageId,
                        campaign_id: campaignId
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        ModalSystem.toast('Error deleting  assigned message', 'error');
                        return response.json().then(data => { throw data; });
                    }
                    return response.json();
                })
                .then(data => {
                    ModalSystem.toast(`✅ Deleted ${data.deleted} assignments for this message.`, 'success');
                    location.reload();
                })
                .catch(error => {
                    console.error(error);
                    ModalSystem.toast('❌ Error deleting assignments for this message.', 'error');
                });
            }
        }); 
    }

    function AssignMessage2Sequence(campaignId) {
        const messageId = document.getElementById('message-select').value;
        const DelayedFromLastMsg= document.getElementById('delayed-from-last-message').value;

        if (!messageId) {
            ModalSystem.toast('Please select a message first.', 'info');
            return;
        }

        fetch("/api/message-assignments/bulk-create/", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRFToken": '{{ csrf_token }}',
            },
            body: JSON.stringify({
                campaign_id: campaignId,
                message_id: messageId,
                delayed_by_days: DelayedFromLastMsg
            })
        })
        .then(res => res.json())
        .then(data => {
            if (data.created !== undefined) {
                ModalSystem.toast(`✅ Assigned to ${data.created} leads.`, 'success');
                document.getElementById('add-modal').close(); // Close modal
                location.reload(); // Or update the DOM dynamically
            } else {
                ModalSystem.toast("❌ Failed to assign message.", 'error');
                console.error(data);
            }
        })
        .catch(err => {
            console.error(err);
            ModalSystem.toast("❌ Error assigning message.", 'error');
        });
    }



    // Action functions
    function viewAssignmentDetails(assignmentId) {
        console.log('View details for assignment:', assignmentId);
        // Implement your detail view logic here
    }

    function resendAssignment(assignmentId) {
        window.ModalSystem.confirm({
            title: 'Resend Message',
            message: 'Are you sure you want to resend this message?',
            confirmText: 'Resend',
            confirmClass: 'btn-primary',
            action: () => {
                window.ModalSystem.loading('Resending message...');
                // Implement your resend logic here
                console.log('Resending assignment:', assignmentId);
                setTimeout(() => {
                    window.ModalSystem.hideLoading();
                    window.ModalSystem.success('Message resent successfully!');
                }, 1000);
            }
        });
    }

    function exportAssignments() {
        if (!currentMessageId) return;
        window.ModalSystem.loading('Preparing export...');
        console.log('Exporting assignments for message:', currentMessageId);
        // Implement your export logic here
        setTimeout(() => {
            window.ModalSystem.hideLoading();
            window.ModalSystem.success('Export completed successfully!');
        }, 1500);
    }
</script>
{% endblock %}