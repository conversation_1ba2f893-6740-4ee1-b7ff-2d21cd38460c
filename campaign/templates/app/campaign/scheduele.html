{% extends "app/campaign/campaign-base.html" %}

{% block title %}Campaign schedule{% endblock %}
 
{% block js %}
<script type="module" src="https://unpkg.com/cally"></script>
{% endblock %}

{% block cmp-base-content %}

<style>
/* Custom styles for schedule page */
.schedule-item {
    transition: all 0.2s ease;
}

.schedule-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.schedule-item.active {
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
}

.delete-schedule-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.schedule-item:hover .delete-schedule-btn {
    opacity: 1;
}

.delete-schedule-btn:hover {
    color: #ef4444 !important;
}

.time-select, .timezone-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.form-control input:focus,
.form-control select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.time-select:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}
</style>

        

        {% if not schedule %}
          <!-- div two-column layout -->
        <div id="deafault-view-form" class="flex-1 flex gap-8 mt-6 overflow-hidden">
          <!-- Left Column: Schedule List -->
          <div class="w-full max-w-xs flex-shrink-0 flex flex-col gap-4">
            <!-- Campaign Dates -->
            <div class="space-y-3 text-sm">
                <div class="flex items-center gap-3">
                    <i class="fa-regular fa-calendar-days w-4 text-center text-gray-500"></i>
                    <span class="font-semibold text-gray-600">Start</span>
                    <div class="w-full border-b border-dashed"></div>
                    <button class="font-semibold text-primary">Now</button>
                </div>
                <div class="flex items-center gap-3">
                    <i class="fa-regular fa-calendar-days w-4 text-center text-gray-500"></i>
                    <span class="font-semibold text-gray-600">End</span>
                    <div class="w-full border-b border-dashed"></div>
                    <button class="font-semibold text-primary whitespace-nowrap">When Finished</button>
                </div>
            </div>

            <div class="divider my-1"></div>

            <!-- Schedules List -->
            <div class="space-y-3">
                <!-- Default Schedule -->
                <div class="bg-white border-2 border-primary rounded-lg p-3 flex items-center justify-between cursor-pointer schedule-item active"
                     data-schedule-id="1">
                    <div class="flex items-center gap-2 text-gray-800 font-semibold">
                        <i class="fa-regular fa-calendar-days"></i>
                        <span class="schedule-name">Default Schedule</span>
                    </div>
                </div>
            </div>

          </div>

          <!-- Right Column: Schedule Editor -->
          <div class="flex-1 flex flex-col gap-6 overflow-y-auto">
            <form class="space-y-6" onsubmit="event.preventDefault()">
                <!-- Schedule Name Card -->
                <div class="card bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-gray-800 text-xl mb-4">Schedule Name</h3>
                    <h3 class="text-neutral text-lg mb-4 ml-4">Default Schedule</h3>
                </div>

                <!-- Timing Card -->
                <div class="card bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-gray-800 text-lg mb-4">Timing</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        <div class="form-control">
                            <label class="label"><span class="label-text">From</span></label>
                            <h3 class="text-neutral text-lg mb-4 ml-4">Midnight | 12:00 AM</h3>
                        </div>
                        <div class="form-control">
                            <label class="label"><span class="label-text">To</span></label>
                            <h3 class="text-neutral text-lg mb-4 ml-4">11:00 PM</h3>
                        </div>
                    </div>
                    <div class="form-control">
                        <label class="label"><span class="label-text">Timezone</span></label>
                        <h3 class="text-neutral text-lg mb-4 ml-4">Europe/London | (UTC±00:00) Greenwich Mean Time</h3>
                    </div>
                </div>



                <!-- Days Card -->
                <div class="card bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-gray-800 text-lg mb-4">Days</h3>
                    <div class="flex flex-wrap gap-x-6 gap-y-3">
                        <h3 class="text-neutral text-lg mb-4 ml-4">Monday</h3>
                        <h3 class="text-neutral text-lg mb-4 ml-4">Tuesday</h3>
                        <h3 class="text-neutral text-lg mb-4 ml-4">Wednesday</h3>
                        <h3 class="text-neutral text-lg mb-4 ml-4">Thursday</h3>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button type="submit" class="btn btn-primary" onclick="editDeafaultForm()">
                        <i class="fa-solid fa-edit mr-2"></i>Edit Default Schedule
                    </button>
                </div>
            </form>
          </div>
        </div>

        {% endif %}


          <!-- div two-column layout -->
        <div id=editable-form class="flex-1 flex gap-8 mt-6 overflow-hidden {% if not schedule %}hidden{% endif %}">
          <!-- Left Column: Schedule List -->
          <div class="w-full max-w-xs flex-shrink-0 flex flex-col gap-4">
            <!-- Campaign Dates -->
            <div class="space-y-3 text-sm">
               <div class="flex items-center gap-3">
                    <i class="fa-regular fa-calendar-days w-4 text-center text-gray-500"></i>
                    <span class="font-semibold text-gray-600">Start</span>
                    <div class="w-full border-b border-dashed"></div>
                    
                    <!-- Hidden date input -->
                    <input type="date" 
                        id="date-picker" 
                        class="hidden"
                        {% if schedule.0.start_date %}
                            value="{{ schedule.0.start_date|date:'Y-m-d' }}"
                        {% else %}
                            value="{% now 'Y-m-d' %}"
                        {% endif %}>
                    
                    <!-- Visible button that triggers the date picker -->
                    <button type="button" class="font-semibold text-primary" id="campaign-start">
                        {% if schedule %}{{ schedule.0.start_date|date:"d/m/Y" }}{% else %}Now{% endif %}
                    </button>
                </div>

                <!-- This will display the selected date (optional) -->
                <div id="selected-date" class="text-sm text-gray-500 mt-1"></div>
                <div class="flex items-center gap-3">
                    <i class="fa-regular fa-calendar-days w-4 text-center text-gray-500"></i>
                    <span class="font-semibold text-gray-600">End</span>
                    <div class="w-full border-b border-dashed"></div>
                    <button class="font-semibold text-primary whitespace-nowrap" id="campaign-end">When Finished</button>
                </div>
            </div>

            <div class="divider my-1"></div>

            <!-- Schedules List -->
            <div class="space-y-3" id="schedules-container">
                <!-- Default Schedule -->
                <div class="bg-white border-2 border-primary rounded-lg p-3 flex items-center justify-between cursor-pointer schedule-item active">
                    <div class="flex items-center gap-2 text-gray-800 font-semibold">
                        <i class="fa-regular fa-calendar-days"></i>
                        <span id="right-schedule-name" class="schedule-name">{% if schedule %}{{ schedule.0.name }}{% else %}Default Schedule{% endif %}</span>
                    </div>
                    {% if schedule %}
                    <button class="btn btn-ghost btn-sm btn-square text-gray-400 delete-schedule-btn"
                            onclick="confirmResetSchedule2Default()" title="Delete schedule">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                    {% endif %}
                </div>
            </div>
            {% if schedule %}
            <button class="btn btn-ghost bg-white border border-gray-200 mt-2 text-primary font-semibold"
                    onclick="confirmResetSchedule2Default()">
                <i class="fa-solid fa-undo mr-2"></i>Back to default schedule
            </button>
            {% else %}
            <button class="btn btn-ghost bg-white border border-gray-200 mt-2 text-primary font-semibold"
                    onclick="saveSchedule()">
                <i class="fa-solid fa-save mr-2"></i>save Schedule
            </button>
            {% endif %}

          </div>

          <!-- Right Column: Schedule Editor -->
          <div class="flex-1 flex flex-col gap-6 overflow-y-auto">
            <form id="schedule-form" class="space-y-6" onsubmit="event.preventDefault()">
                <!-- Schedule Name Card -->
                <div class="card bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-gray-800 text-lg mb-4">Schedule Name</h3>
                    <input type="text"
                           id="schedule-name"
                           name="name"
                           value="{% if schedule %}{{ schedule.0.name }}{% else %}Default Schedule{% endif %}"
                           class="input input-bordered w-full bg-white"
                           placeholder="Enter schedule name" 
                           required
                           onchange="syncWithRightSide(value)"/>
                </div>

                <!-- Timing Card -->
                <div class="card bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-gray-800 text-lg mb-4">Timing</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        <div class="form-control">
                            <label class="label"><span class="label-text">From</span></label>
                            <select id="time-from" name="timeFrom" class="select select-bordered bg-white time-select" required onchange="updateToTimes()">
                                {% for time in times %}
                                    <option value="{{time.0}}" {% if schedule.0.timing_from == time.0 %}selected{% endif %}>{{time.1}}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-control">
                            <label class="label"><span class="label-text">To</span></label>
                            <select id="time-to" name="timeTo" class="select select-bordered bg-white time-select" required>
                                {% if not schedule.0.timing_from %}
                                    <option value="10:00" selected>10:00 AM</option>
                                 {% endif %}    
                                {% for time in times %}
                                    {% if schedule.0.timing_from and time.0 > schedule.0.timing_from %}
                                        <option value="{{time.0}}" {% if schedule.0.timing_to == time.0 %}selected{% endif %}>{{time.1}}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-control">
                        <label class="label"><span class="label-text">Timezone</span></label>
                        <select id="timezone" name="timezone" class="select select-bordered bg-white timezone-select" required>
                            {% for tz in timezones %}
                                <option value="{{ tz.0 }}" {% if tz.0 == schedule.0.time_zone %}selected{% endif %}>{{ tz.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>



                <!-- Days Card -->
                <div class="card bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-gray-800 text-lg mb-4">Days</h3>
                    <div class="flex flex-wrap gap-x-6 gap-y-3">
                        {% for day in days %}
                            <label class="label cursor-pointer justify-start gap-2">
                                <input type="checkbox" name="days" value="{{ day.0 }}" {% if day.0 in schedule.0.days %}checked{% endif %} class="checkbox checkbox-primary" />
                                <span class="label-text">{{ day.1 }}</span>
                            </label>
                        {% endfor %}
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button type="submit" class="btn btn-primary" onclick="saveSchedule()">
                        <i class="fa-solid fa-save mr-2"></i>Save Schedule
                    </button>
                    <button type="button" class="btn btn-ghost" onclick="resetForm()">
                        <i class="fa-solid fa-undo mr-2"></i>Reset Form
                    </button>
                </div>
            </form>
          </div>
        </div>


<script>
function editDeafaultForm() {
    editForm= document.getElementById('editable-form');
    deafaultForm= document.getElementById('deafault-view-form');

    // display none for the default form
    deafaultForm.style.display = "none";
    
    editForm.classList.remove('hidden');

}
function updateToTimes() {
    const fromSelect = document.getElementById('time-from');
    const toSelect = document.getElementById('time-to');
    const selectedFromTime = fromSelect.value;
    
    // Enable To select
    toSelect.disabled = false;
    
    // Clear existing options
    toSelect.innerHTML = '';
    
    // Add only times after the selected From time
    const times = [
        {% for time in times %}
            {value: "{{ time.0 }}", label: "{{ time.1 }}"},
        {% endfor %}
    ];
    
    times.forEach(time => {
        if (time.value > selectedFromTime) {
            const option = document.createElement('option');
            option.value = time.value;
            option.textContent = time.label;
            toSelect.appendChild(option);
        }
    });
    
    // If no valid options, disable the select
    if (toSelect.options.length === 0) {
        toSelect.disabled = true;
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    {% if schedule.timing_from %}
        updateToTimes();
    {% endif %}
});

function syncWithRightSide(CurrentTxt) {
    rightSide= document.getElementById('right-schedule-name')
    rightSide.innerHTML= CurrentTxt;
} 

document.getElementById('campaign-start').addEventListener('click', function() {
    const dateInput = document.getElementById('date-picker');
    
    // Set min date to today (disable past dates)
    const today = new Date().toISOString().split('T')[0];
    dateInput.setAttribute('min', today);

    // Pre-select existing date if available
    {% if schedule.0.start_date %}
        dateInput.value = dateInput.value;
    {% else %}
        dateInput.value = today; // Default to today
    {% endif %}
    
    // Show picker
    dateInput.showPicker();
    
    dateInput.addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        const isoDate = selectedDate.toISOString();
        
        // Update button text in dd/mm/yyyy format
        document.getElementById('campaign-start').textContent = 
            selectedDate.toLocaleDateString('en-GB'); 
        window.selectedStartDate = isoDate;
    });
});


function saveSchedule() {
    // Validate at least one day is checked
    const checkboxes = document.querySelectorAll('input[name="days"]:checked');
    if (checkboxes.length === 0) {
        ModalSystem.toast("Please select at least one day!", 'error');
        return;
    }

    // Collect all form values
    const startDate = window.selectedStartDate || new Date().toISOString();
    const form = document.getElementById('schedule-form');
    const formData = {
        name: form.querySelector('#schedule-name').value,
        start_date: startDate,
        timing_from: form.querySelector('#time-from').value,
        timing_to: form.querySelector('#time-to').value,
        time_zone: form.querySelector('#timezone').value,
        days: Array.from(checkboxes).map(checkbox => checkbox.value),
        campaign: {{ campaign_id }},
        subscribed_company: {{ request.user.subscribed_company.id }}
    };

    // Send POST request
    fetch('/api/schedule/{% if not schedule %}/{% else %}{{ schedule.0.id }}/{% endif %}', {
        method: {% if not schedule %}'POST'{% else %}'PUT'{% endif %},
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => { throw err; });
        }
        return response.json();
    })
    .then(data => {
        ModalSystem.toast('Schedule {% if not schedule %}created{% else %}updated{% endif %} successfully!', 'success');
        // window.location.href = `/campaign/campaign-options/{{campaign_id}}`;
    })
    .catch(error => {
        console.error('Error:', error);
        ModalSystem.toast('Failed to {% if not schedule %}created{% else %}updated{% endif %} schedule, please try again!', 'error');
    });
}



function resetForm() {
    // Reset text input
    document.getElementById('schedule-name').value = '';
    
    // Reset date picker
    const today = new Date();
    const datePicker = document.getElementById('date-picker');
    datePicker.value = today.toISOString().split('T')[0];
    document.getElementById('campaign-start').textContent = 'Now';
    
    // Reset time selects to default values
    document.getElementById('time-from').selectedIndex = 0;
    document.getElementById('time-to').selectedIndex = 0;
    
    // Reset timezone
    document.getElementById('timezone').selectedIndex = 0;
    
    // Uncheck all day checkboxes
    document.querySelectorAll('input[name="days"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}


function confirmResetSchedule2Default() {
    const startDate = new Date().toISOString();
    const defaultData = {
        name: 'Default Schedule',
        start_date: startDate,
        timing_from: '00:00',
        timing_to: '23:00',
        time_zone: 'Europe/London',
        days: ['mon', 'tue', 'wed', 'thu'],
    };

    // Create a nicely formatted HTML message
    const daysMap = {
        mon: 'Monday',
        tue: 'Tuesday',
        wed: 'Wednesday',
        thu: 'Thursday',
        fri: 'Friday'
    };
    
    const formattedDate = new Date(defaultData.start_date).toLocaleDateString('en-GB');
    const formattedDays = defaultData.days.map(day => daysMap[day]).join(', ');
    
    const htmlMessage = `
        <div class="space-y-2">
            <p>Are you sure you want to reset to default values?</p>
            <div class="bg-base-200 p-4 rounded-lg">
                <h4 class="font-bold mb-2">Default values will be:</h4>
                <ul class="list-disc pl-5 space-y-1">
                    <li><strong>Name:</strong> ${defaultData.name}</li>
                    <li><strong>Start Date:</strong> ${formattedDate}</li>
                    <li><strong>Timing:</strong> ${defaultData.timing_from} to ${defaultData.timing_to}</li>
                    <li><strong>Timezone:</strong> ${defaultData.time_zone.replace('_', ' ')}</li>
                    <li><strong>Days:</strong> ${formattedDays}</li>
                </ul>
            </div>
        </div>
    `;

    ModalSystem.confirm({
        title: 'Reset to Default Schedule',
        message: htmlMessage,
        confirmText: 'Reset to Default',
        confirmClass: 'btn-error',
        html: true, // This enables HTML rendering
        action: function() {
            fetch(`/api/schedule/{{ schedule.0.id }}/`, {
                method: 'PUT',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(defaultData)
            })
            .then(response => {
                if (!response.ok) throw new Error('Failed to reset');
                ModalSystem.toast('Schedule reset successfully!', 'success');
                window.location.reload();
            })
            .catch(error => {
                ModalSystem.toast('Error resetting schedule', 'error');
                console.error('Error:', error);
            });
        }
    });
}
</script>

{% endblock %}