{% extends "app/campaign/campaign-base.html" %}
{% load static %}
{% csrf_token %}

{% block title %}Campaign Dashboard{% endblock %}

{% block js %}
<script src="{% static 'cdn/chart.js' %}"></script>
{% endblock %}



{% block cmp-base-content %}

<style>
/* Custom styles for dashboard */
.stat-card {
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.progress-ring {
    transition: stroke-dasharray 0.5s ease;
}

.dropdown-content {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.tab-content {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-content.active {
    opacity: 1;
}

.activity-item {
    transition: all 0.2s ease;
}

.activity-item:hover {
    background-color: #f9fafb;
}
</style>

        <!-- Status & Filters -->
        <div class="flex items-center justify-between mt-8">
            <div class="flex items-center gap-3">
                <span class="text-sm font-medium text-gray-500">Status:</span>
                    {% if status == "active" %}
                        <span class="badge badge-success text-white font-medium py-2">Active</span>
                    {% elif status == "paused" %}
                        <span class="badge badge-warning text-white font-medium py-2">Paused</span>
                    {% elif status == "draft" %}
                        <span class="badge badge-neutral text-white font-medium py-2">Draft</span>
                    {% elif status == "completed" %}
                        <span class="badge badge-info text-white font-medium py-2">Completed</span>
                    {% else %}
                        {{ status }}
                    {% endif %}
                <span class="mr-2 ml-2">|</span>    
                <span class="text-sm font-bold text-gray-800" id="campaign-progress-text">{{stats.sequence_started_rate}}%</span>
                <progress class="progress progress-primary w-40 h-1.5" value="{{stats.sequence_started_rate}}" max="100" id="campaign-progress-bar"></progress>
                <span class="text-sm font-medium text-gray-500" id="campaign-progress-text">out of <span class="text-2xl text-success font-bold mr-1 ml-1">{{stats.total_leads}}</span> total leads</span>
            </div>
            <div class="flex items-center gap-3">
                <button class="btn bg-white border border-base-300 h-10 min-h-10 rounded-lg px-4 text-sm hover:bg-gray-50" onclick="">
                    <i class="fa-solid fa-share-nodes mr-2 text-gray-500"></i>
                    Sending Status
                </button>
                <div class="dropdown dropdown-end">
                    <button tabindex="0" role="button" class="btn bg-white border border-base-300 h-10 min-h-10 px-3 flex items-center gap-2 rounded-lg hover:bg-gray-50">
                        <span class="text-sm font-medium text-gray-700" id="time-filter-text">Last 4 weeks</span>
                        <i class="fa-solid fa-chevron-down text-xs text-gray-500"></i>
                    </button>
                    <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-white rounded-box w-52 mt-2">
                        <li><a onclick="changeTimeFilter('24h', 'Last 24 hours')">Last 24 hours</a></li>
                        <li><a onclick="changeTimeFilter('7d', 'Last 7 days')">Last 7 days</a></li>
                        <li><a onclick="changeTimeFilter('4w', 'Last 4 weeks')" class="active">Last 4 weeks</a></li>
                        <li><a onclick="changeTimeFilter('3m', 'Last 3 months')">Last 3 months</a></li>
                        <li><a onclick="changeTimeFilter('all', 'All time')">All time</a></li>
                    </ul>
                </div>
                <button class="btn bg-white border border-base-300 btn-square h-10 min-h-10 w-10 hover:bg-gray-50" onclick="openSettings()">
                    <i class="fa-solid fa-gear text-gray-500"></i>
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mt-6" id="stats-container">

            <!-- Card 2: Open Rate -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm stat-card cursor-pointer">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium">
                    <span>Open rate</span>
                    <div class="tooltip tooltip-center" data-tip="Number of leads that have opened at least one email in this campaign.">
                        <i class="fa-solid fa-circle-info hover:text-gray-600 cursor-help"></i>
                    </div>
                </div>
                <div class="mt-4 text-4xl font-bold text-gray-800">
                    <span id="opens-count">{{stats.opened_count}}</span>
                    <span class="text-gray-300 font-light mx-1">|</span>
                    <span class="text-2xl font-semibold" id="open-rate">{{stats.open_rate_percentage}}%</span>
                </div>
            </div>

            <!-- Card 3: Click Rate -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm stat-card cursor-pointer">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium">
                    <span>Click rate</span>
                    <div class="tooltip tooltip-center" data-tip="Number of leads that have clicked at least one link in this campaign.">
                        <i class="fa-solid fa-circle-info hover:text-gray-600 cursor-help"></i>
                    </div>
                </div>
                <div class="mt-4 text-4xl font-bold text-gray-800">
                    <span id="clicks-count">{{stats.clicked_count}}</span>
                    <span class="text-gray-300 font-light mx-1">|</span>
                    <span class="text-2xl font-semibold" id="click-rate">{{stats.click_rate_percentage}}%</span>
                </div>
            </div>

            <!-- Card : reply rate -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm stat-card cursor-pointer">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium">
                    <span>Reply rate</span>
                    <div class="tooltip tooltip-center" data-tip="Number of leads that have replied at least one email in this campaign.">
                        <i class="fa-solid fa-circle-info hover:text-gray-600 cursor-help"></i>
                    </div>
                </div>
                <div class="mt-4 text-4xl font-bold text-gray-800">
                    <span id="sequence-started">{{stats.replied_count}}</span>
                    <span class="text-gray-300 font-light mx-1">|</span>
                    <span class="text-2xl font-semibold">{{stats.reply_rate_percentage}}%</span>
                </div>
            </div>

            <!-- Card 4: Opportunities -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm stat-card cursor-pointer">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium">
                    <span>Opportunities</span>
                    <div class="tooltip tooltip-center" data-tip="Number of leads that you have marked as positive. Per positive lead value is set to $2,333 Click to update">
                        <i class="fa-solid fa-circle-info hover:text-gray-600 cursor-help"></i>
                    </div>
                </div>
                <div class="mt-4 text-4xl font-bold text-gray-800">
                    <span id="opportunities-count">{{stats.opportunities_count}}</span>
                    <span class="text-gray-300 font-light mx-1">|</span>
                    <span class="text-2xl font-semibold text-gray-500" id="opportunities-value">${{stats.opportunities_total}}</span>
                </div>
            </div>

            <!-- Card 5: Conversions -->
            <div class="bg-white border border-gray-200 rounded-xl p-5 shadow-sm stat-card cursor-pointer">
                <div class="flex justify-between items-center text-gray-400 text-sm font-medium">
                    <span>Conversions</span>
                    <div class="tooltip tooltip-left" data-tip="The total number of won opportunities and its total $ amount">
                        <i class="fa-solid fa-circle-info hover:text-gray-600 cursor-help"></i>
                    </div>
                </div>
                <div class="mt-4 text-4xl font-bold text-gray-800">
                    <span id="conversions-count">{{stats.conversions_count}}</span>
                    <span class="text-gray-300 font-light mx-1">|</span>
                    <span class="text-2xl font-semibold text-gray-500" id="conversions-value">${{stats.conversion_total}}</span>
                </div>
            </div>
        </div>
        
        <!-- Chart Section -->
        <div class="mt-6 bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Performance Overview</h3>
                <div class="flex items-center gap-2">
                    <!-- Chart Legend -->
                    <div class="flex items-center gap-4 text-sm" id="chart-legend" style="display: none;">
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-gray-600">Sent</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">Opened</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                            <span class="text-gray-600">Clicked</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span class="text-gray-600">Replied</span>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-ghost" onclick="refreshChart()" id="refresh-btn">
                        <i class="fa-solid fa-refresh mr-2"></i>Refresh
                    </button>
                </div>
            </div>
            
<!-- Main Analytics Chart -->
<div class="card bg-base-100 shadow-lg mb-8">
    <div class="card-body">
        <!-- Custom Legend - Updated to match new order -->
        <div class="flex flex-wrap gap-x-4 gap-y-2 mb-4">
            <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-purple-400"></div>Total replies</div>
            <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-amber-400"></div>Total clicks</div>
            <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-sky-400"></div>Total opens</div>
            <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-blue-500"></div>Sent</div>
            <div class="flex items-center gap-2 text-sm"><div class="w-3 h-3 rounded-full bg-teal-400"></div>Opportunities</div>
        </div>
        <div class="chart-container" style="height: 400px;">
            <canvas id="mainAnalyticsChart"></canvas>
        </div>
    </div>
</div>
        </div>

        <!-- Step Analytics & Activity -->
        <div class="mt-6 bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div role="tablist" class="tabs tabs-boxed bg-gray-100 w-max">
                <a role="tab" class="tab tab-active bg-white text-primary shadow-sm" onclick="switchTab('analytics')">Step Analytics</a>
                <a role="tab" class="tab" onclick="switchTab('activity')">Activity</a>
            </div>

            <!-- Step Analytics Tab -->
            <div class="mt-6 tab-content active" id="analytics-tab">
                <div class="space-y-4" id="step-analytics-container">
                    <p class="text-sm text-gray-600">👋 Step analytics will appear here once the campaign is published</p>
                </div>
            </div>

            <!-- Activity Tab -->
            <div class="mt-6 tab-content" id="activity-tab">
                <div class="space-y-3" id="activity-container">
                    <p class="text-sm text-gray-600">Recent campaign activity will appear here</p>
                    <button class="btn btn-sm btn-outline btn-primary" onclick="loadActivity()">
                        Load Sample Activity
                    </button>
                </div>
            </div>
        </div>

{{ campaign.id|json_script:"campaign-id" }}
<script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let mainAnalyticsChart;
        let currentTimeFilter = '4w';
        const campaignId = {{ campaign_id }};

        // API functions
        const fetchAnalyticsData = async (timeFilter = '4w') => {
            try {
                const response = await fetch(`/campaign/api/analytics/${campaignId}?filter=${timeFilter}`);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching analytics data:', error);
                return null;
            }
        };

        const refreshStats = async () => {
            try {
                const refreshBtn = document.getElementById('refresh-btn');
                refreshBtn.disabled = true;
                refreshBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Refreshing...';

                const response = await fetch(`/campaign/api/refresh-stats/${campaignId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': Cookies.get('csrftoken'),
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // Reload the chart data after a short delay
                    setTimeout(() => {
                        loadAnalyticsData(currentTimeFilter);
                    }, 2000);
                } else {
                    console.error('Error refreshing stats:', result.message);
                }
            } catch (error) {
                console.error('Error refreshing stats:', error);
            } finally {
                const refreshBtn = document.getElementById('refresh-btn');
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fa-solid fa-refresh mr-2"></i>Refresh';
            }
        };

        // Update summary cards with new data
        const updateSummaryCards = (summaryStats) => {
            document.getElementById('opens-count').textContent = summaryStats.opened_count;
            document.getElementById('open-rate').textContent = summaryStats.open_rate_percentage + '%';
            document.getElementById('clicks-count').textContent = summaryStats.clicked_count;
            document.getElementById('click-rate').textContent = summaryStats.click_rate_percentage + '%';
            document.getElementById('sequence-started').textContent = summaryStats.replied_count;
            document.getElementById('opportunities-count').textContent = summaryStats.opportunities_count;
            document.getElementById('opportunities-value').textContent = '$' + summaryStats.opportunities_total_value.toLocaleString();
            document.getElementById('conversions-count').textContent = summaryStats.conversions_count;
            document.getElementById('conversions-value').textContent = '$' + summaryStats.conversions_total_value.toLocaleString();

            // Update progress bar
            document.getElementById('campaign-progress-text').textContent = summaryStats.sequence_started_rate + '%';
            document.getElementById('campaign-progress-bar').value = summaryStats.sequence_started_rate;
        };

        const chartOptions = (theme) => {
            const isDark = theme === 'dark';
            return {
                maintainAspectRatio: false,
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: isDark ? '#1f2937' : '#f9fafb',
                        titleColor: isDark ? '#f3f4f6' : '#1f2937',
                        bodyColor: isDark ? '#e5e7eb' : '#1f2937',
                        borderColor: isDark ? '#374151' : '#e5e7eb',
                        borderWidth: 1,
                        padding: 10
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: isDark ? '#e5e7eb' : '#374151',
                            font: {
                                size: 12,
                                family: 'Inter, sans-serif',
                            }
                        },
                        grid: {
                            color: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        }
                    },
                    x: {
                        ticks: {
                            color: isDark ? '#e5e7eb' : '#374151',
                            font: {
                                size: 12,
                                family: 'Inter, sans-serif',
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },
                elements: {
                    line: {
                        borderWidth: 3,
                        tension: 0.4,
                        borderCapStyle: 'round',
                        borderJoinStyle: 'round'
                    },
                    point: {
                        radius: 3,
                        hoverRadius: 6,
                        hitRadius: 10
                    }
                }
            };
        };

        const createOrUpdateChart = (analyticsData) => {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const ctx = document.getElementById('mainAnalyticsChart');
            if (!ctx || !analyticsData) return;

            // Sort datasets by maximum value (lowest to highest for better visibility)
            const datasets = [...analyticsData.chart_data.datasets];
            datasets.sort((a, b) => {
                const maxA = Math.max(...a.data);
                const maxB = Math.max(...b.data);
                return maxA - maxB;
            });

            const chartData = {
                labels: analyticsData.chart_data.labels,
                datasets: datasets
            };

            if (mainAnalyticsChart) {
                mainAnalyticsChart.data = chartData;
                mainAnalyticsChart.options = chartOptions(currentTheme);
                mainAnalyticsChart.update();
            } else {
                mainAnalyticsChart = new Chart(ctx.getContext('2d'), {
                    type: 'line',
                    data: chartData,
                    options: chartOptions(currentTheme)
                });
            }
        };

        // Load analytics data and update chart
        const loadAnalyticsData = async (timeFilter = '4w') => {
            const data = await fetchAnalyticsData(timeFilter);
            if (data && data.success) {
                createOrUpdateChart(data);
                updateSummaryCards(data.summary_stats);
            }
        };

        // Time filter change handler
        window.changeTimeFilter = (filterValue, displayText) => {
            currentTimeFilter = filterValue;
            document.getElementById('time-filter-text').textContent = displayText;

            // Update active state in dropdown
            document.querySelectorAll('.dropdown-content a').forEach(a => a.classList.remove('active'));
            event.target.classList.add('active');

            // Load new data
            loadAnalyticsData(filterValue);
        };

        // Refresh chart handler
        window.refreshChart = () => {
            refreshStats();
        };

        // Initialize the dashboard
        loadAnalyticsData(currentTimeFilter);

        // Theme change observer
        const observer = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    // Reload chart with current data but new theme
                    loadAnalyticsData(currentTimeFilter);
                }
            }
        });
        observer.observe(document.documentElement, { attributes: true });
    });
</script>

{% endblock %}