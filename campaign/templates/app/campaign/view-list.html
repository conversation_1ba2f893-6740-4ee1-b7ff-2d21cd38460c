{% extends "base-app.html" %}
{% block title %}Campaigns{% endblock %}
{% load mathfilters %}
{% block content %}
<div class="flex-1 flex flex-col overflow-hidden bg-gray-50 p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Campaigns</h1>
                <p class="text-gray-600">Manage your campaigns and view performance</p>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-primary" onclick="addNewCampaign()">
                    <i class="fa-solid fa-plus mr-2"></i>
                    New Campaign
                </button>
            </div>
        </div>
    </div>

    <!-- Performance Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- ... (keep your existing stats cards) ... -->
    </div>

    <!-- Filters and Search -->
    <div class="mb-6 bg-white rounded-lg p-4 shadow-sm border border-gray-200">
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex-1 min-w-64">
                <input type="text" placeholder="Search campaigns by name or product..." 
                       class="input input-bordered w-full bg-white" 
                       id="search-campaigns">
            </div>
            <select class="select select-bordered bg-white" id="filter-status">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="paused">Paused</option>
                <option value="draft">Draft</option>
                <option value="completed">Completed</option>
            </select>
            <button onclick="clearCampaignFilters()" class="btn btn-ghost">Clear</button>
            <div class="text-sm text-gray-500">
                <span id="campaign-count">{{ campaigns|length }} campaigns</span>
            </div>
        </div>
    </div>

    <!-- Campaigns Table -->
    <div class="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-left font-semibold text-gray-900 pl-6">Campaign</th>
                        <th class="text-left font-semibold text-gray-900">Status</th>
                        <th class="text-left font-semibold text-gray-900">Progress</th>
                        <th class="text-left font-semibold text-gray-900">Sent</th>
                        <th class="text-left font-semibold text-gray-900">Clicked</th>
                        <th class="text-left font-semibold text-gray-900">Replied</th>
                        <th class="text-left font-semibold text-gray-900">Opportunities</th>
                        <th class="text-left font-semibold text-gray-900">Conversions</th>
                        <th class="text-left font-semibold text-gray-900">Actions</th>
                    </tr>
                </thead>
                <tbody id="campaigns-table-body">
                    {% for campaign in campaigns %}
                    <tr class="hover:bg-gray-50 cursor-pointer campaign-row" 
                        onclick="viewCampaign({{ campaign.id }})"
                        data-status="{{ campaign.status|lower }}">
                        <td class="pl-6">
                            <div class="font-bold text-gray-800">{{ campaign.name }}</div>
                            <div class="text-sm text-gray-500">{{ campaign.product }}</div>
                        </td>
                        <td>
                            {% if campaign.status == "active" %}
                                <span class="badge badge-success text-white font-medium py-2">Active</span>
                            {% elif campaign.status == "paused" %}
                                <span class="badge badge-warning text-white font-medium py-2">Paused</span>
                            {% elif campaign.status == "draft" %}
                                <span class="badge badge-neutral text-white font-medium py-2">Draft</span>
                            {% elif campaign.status == "completed" %}
                                <span class="badge badge-info text-white font-medium py-2">Completed</span>
                            {% else %}
                                {{ campaign.status }}
                            {% endif %}
                        </td>
                        <td>
                            <div class="flex items-center gap-2">
                                <progress class="progress progress-primary w-20" 
                                    value="{% if campaign.campaign_stats.total_leads > 0 %}{{ campaign.campaign_stats.sequence_started_count|div:campaign.campaign_stats.total_leads|mul:100|floatformat:0 }}{% else %}0{% endif %}"
                                    max="100">
                                </progress>
                                <span class="text-sm font-medium text-gray-600">
                                    {{ campaign.campaign_stats.sequence_started_count|div:campaign.campaign_stats.total_leads|mul:100|floatformat:0 }}% : {{ campaign.campaign_stats.sequence_started_count }} / {{ campaign.campaign_stats.total_leads }}
                                </span>
                            </div>
                        </td>
                        <td class="font-medium text-gray-700">{{ campaign.campaign_stats.opened_count }}</td>
                        <td class="font-medium text-gray-700">{{ campaign.campaign_stats.clicked_count }}</td>
                        <td class="font-medium text-gray-700">{{ campaign.campaign_stats.replied_count }}</td>
                        <td class="font-medium text-gray-700">{{ campaign.campaign_stats.opportunities_count }}</td>
                        <td class="font-medium text-gray-700">{{ campaign.campaign_stats.conversions_count }}</td>
                        <td>
                            <div class="flex items-center gap-0">
                                <button class="btn btn-ghost btn-sm" onclick="event.stopPropagation(); viewCampaign({{ campaign.id }})" title="Edit">
                                    <i class="fa-solid fa-edit"></i>
                                </button>
                                <button class="btn btn-ghost btn-sm text-red-500" onclick="event.stopPropagation(); confirmDeleteCampaign({{ campaign.id }})" title="Delete">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr id="no-campaigns-row">
                        <td colspan="8" class="text-center py-16">
                            <div class="flex flex-col items-center justify-center">
                                <!-- SVG Illustration -->
                                <div class="mb-6 opacity-90">
                                    <img src="https://raw.githubusercontent.com/Othunderlight/static/refs/heads/main/no-campaign-lead.svg"
                                         alt="No campaigns illustration"
                                         class="w-64 h-auto mx-auto">
                                </div>
                                <!-- Message -->
                                <div class="text-center">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-2">
                                        👋 Create your first campaign
                                    </h3>
                                    <p class="text-gray-500 mb-6">
                                        Get started by creating a new outreach campaign
                                    </p>
                                    <button class="btn btn-primary" onclick="addNewCampaign()">
                                        <i class="fa-solid fa-plus mr-2"></i>
                                        New Campaign
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Campaign filtering functionality
function filterCampaigns() {
    const searchTerm = document.getElementById('search-campaigns').value.toLowerCase();
    const statusFilter = document.getElementById('filter-status').value.toLowerCase();
    
    const rows = document.querySelectorAll('.campaign-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const campaignText = row.textContent.toLowerCase();
        const status = row.getAttribute('data-status').toLowerCase();
        
        const matchesSearch = searchTerm === '' || campaignText.includes(searchTerm);
        const matchesStatus = statusFilter === '' || status === statusFilter;

        if (matchesSearch && matchesStatus) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update empty state
    const noCampaignsRow = document.getElementById('no-campaigns-row');
    if (visibleCount === 0 && rows.length > 0) {
        if (noCampaignsRow) noCampaignsRow.style.display = '';
    } else {
        if (noCampaignsRow) noCampaignsRow.style.display = 'none';
    }

    // Update counter
    document.getElementById('campaign-count').textContent = `${visibleCount} campaigns`;
}

function clearCampaignFilters() {
    document.getElementById('search-campaigns').value = '';
    document.getElementById('filter-status').value = '';
    filterCampaigns();
}

// Action functions
function viewCampaign(id) {
    window.location.href = `/campaign/dashboard/${id}`;
}

function addNewCampaign() {
    window.location.href = '/campaign/campaigns/new/';
}

function editCampaign(id) {
    window.location.href = `/campaigns/${id}/edit/`;
}

function confirmDeleteCampaign(id) {
    ModalSystem.confirm({
        title: 'Delete Campaign',
        message: 'Are you sure you want to delete this campaign? All associated data will be lost.',
        confirmText: 'Delete',
        confirmClass: 'btn-error',
        action: function() {
            fetch(`/api/campaigns/${id}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    ModalSystem.toast('Error deleting Campaign', 'error');
                    throw new Error('Failed to delete Campaign');
                }
                const row = document.querySelector(`tr[onclick="viewCampaign(${id})"]`);
                if (row) row.remove();

                counter= document.getElementById("campaign-count")
                counter.innerHTML= counter.innerHTML - 1;
                
                
                ModalSystem.toast('Campaign deleted successfully!', 'success');
                return response;
            })
            .catch(error => {
                console.error('Error deleting message:', error);
                throw error;
            });
        }
    });
}


// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for filters
    document.getElementById('search-campaigns').addEventListener('input', filterCampaigns);
    document.getElementById('filter-status').addEventListener('change', filterCampaigns);
    
    // Hide empty state if we have campaigns
    const noCampaignsRow = document.getElementById('no-campaigns-row');
    if (noCampaignsRow && document.querySelectorAll('.campaign-row').length > 0) {
        noCampaignsRow.style.display = 'none';
    }
});
</script>
{% endblock %}