version: '3'

services:
  web:
    image: ghcr.io/sudocoder-py/b2b-outreach:latest
    command: >
      sh -c "python manage.py collectstatic --noinput &&
             gunicorn dcrm.wsgi:application --bind 0.0.0.0:8000 --workers 4"
    volumes:
      - static_volume:/app/staticfiles
    ports:
      - "8000:8000"
    env_file:
      - .env.production

  celery:
    image: ghcr.io/sudocoder-py/b2b-outreach:latest
    command: celery -A dcrm worker -l info
    env_file:
      - .env.production
    depends_on:
      - web

volumes:
  static_volume: